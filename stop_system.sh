#!/bin/bash

# 量化交易监控系统停止脚本 (Linux/Mac)
# Quantitative Trading System Stop Script

echo "========================================"
echo "🛑 停止量化交易监控系统"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 停止后端服务
if [ -f "backend/logs/backend.pid" ]; then
    BACKEND_PID=$(cat backend/logs/backend.pid)
    echo -e "${YELLOW}停止后端服务 (PID: $BACKEND_PID)...${NC}"
    kill $BACKEND_PID 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 后端服务已停止${NC}"
    else
        echo -e "${RED}❌ 后端服务停止失败或已停止${NC}"
    fi
    rm -f backend/logs/backend.pid
else
    echo -e "${YELLOW}⚠️ 未找到后端服务PID文件${NC}"
fi

# 停止前端服务
if [ -f "backend/logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat backend/logs/frontend.pid)
    echo -e "${YELLOW}停止前端服务 (PID: $FRONTEND_PID)...${NC}"
    kill $FRONTEND_PID 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 前端服务已停止${NC}"
    else
        echo -e "${RED}❌ 前端服务停止失败或已停止${NC}"
    fi
    rm -f backend/logs/frontend.pid
else
    echo -e "${YELLOW}⚠️ 未找到前端服务PID文件${NC}"
fi

# 强制停止相关进程
echo -e "${YELLOW}检查并清理残留进程...${NC}"
pkill -f "uvicorn app.main:app" 2>/dev/null
pkill -f "npm start" 2>/dev/null
pkill -f "react-scripts start" 2>/dev/null

echo
echo "========================================"
echo -e "${GREEN}✅ 系统已完全停止${NC}"
echo "========================================"
