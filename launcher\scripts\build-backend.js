const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

async function buildBackend() {
  console.log('🔨 开始构建后端...');
  
  const backendPath = path.join(__dirname, '../../backend');
  const outputPath = path.join(__dirname, '../resources/backend');
  
  // 检查后端目录是否存在
  if (!fs.existsSync(backendPath)) {
    console.error('❌ 后端目录不存在:', backendPath);
    process.exit(1);
  }
  
  // 创建输出目录
  if (!fs.existsSync(path.dirname(outputPath))) {
    fs.mkdirSync(path.dirname(outputPath), { recursive: true });
  }
  
  try {
    // 使用PyInstaller打包
    console.log('📦 使用PyInstaller打包后端...');
    
    const pyinstallerArgs = [
      '--onefile',
      '--distpath', outputPath,
      '--workpath', path.join(outputPath, 'build'),
      '--specpath', path.join(outputPath, 'spec'),
      '--name', 'main',
      'app/main.py'
    ];
    
    const pyinstaller = spawn('pyinstaller', pyinstallerArgs, {
      cwd: backendPath,
      stdio: 'inherit'
    });
    
    pyinstaller.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 后端构建成功');
        
        // 复制必要的配置文件
        copyBackendAssets(backendPath, outputPath);
      } else {
        console.error('❌ 后端构建失败，退出代码:', code);
        process.exit(1);
      }
    });
    
    pyinstaller.on('error', (error) => {
      console.error('❌ PyInstaller执行失败:', error);
      console.log('💡 请确保已安装PyInstaller: pip install pyinstaller');
      process.exit(1);
    });
    
  } catch (error) {
    console.error('❌ 构建过程出错:', error);
    process.exit(1);
  }
}

function copyBackendAssets(sourcePath, targetPath) {
  console.log('📋 复制后端资源文件...');
  
  const assetsToCopy = [
    'config',
    'data/mock_data',
    'requirements.txt'
  ];
  
  assetsToCopy.forEach(asset => {
    const sourcePath = path.join(sourcePath, asset);
    const targetPath = path.join(targetPath, asset);
    
    if (fs.existsSync(sourcePath)) {
      try {
        if (fs.statSync(sourcePath).isDirectory()) {
          copyDirectory(sourcePath, targetPath);
        } else {
          fs.copyFileSync(sourcePath, targetPath);
        }
        console.log(`✅ 已复制: ${asset}`);
      } catch (error) {
        console.warn(`⚠️ 复制失败: ${asset} - ${error.message}`);
      }
    } else {
      console.warn(`⚠️ 文件不存在: ${asset}`);
    }
  });
}

function copyDirectory(source, target) {
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
  }
  
  const files = fs.readdirSync(source);
  
  files.forEach(file => {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);
    
    if (fs.statSync(sourcePath).isDirectory()) {
      copyDirectory(sourcePath, targetPath);
    } else {
      fs.copyFileSync(sourcePath, targetPath);
    }
  });
}

// 如果直接运行此脚本
if (require.main === module) {
  buildBackend();
}

module.exports = buildBackend;
