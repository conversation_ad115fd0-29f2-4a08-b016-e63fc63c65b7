@echo off
chcp 65001 >nul
title 量化交易系统启动器
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 量化交易系统启动器                      ║
echo ║                   Quantitative Trading System                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: 检查环境
echo [1/4] 🔍 检查运行环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python环境
    echo 💡 请安装Python 3.8或更高版本
    echo 📥 下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Node.js环境
    echo 💡 请安装Node.js 16或更高版本
    echo 📥 下载地址：https://nodejs.org/
    echo.
    pause
    exit /b 1
)
echo ✅ 环境检查通过

:: 安装依赖
echo [2/4] 📦 安装系统依赖...
if not exist "backend\venv" (
    echo 🔧 创建Python虚拟环境...
    cd backend
    python -m venv venv
    cd ..
)

echo 🔧 安装后端依赖...
cd backend
call venv\Scripts\activate.bat
pip install -q -r requirements.txt >nul 2>&1
cd ..

if not exist "frontend\node_modules" (
    echo 🔧 安装前端依赖...
    cd frontend
    npm install --silent >nul 2>&1
    cd ..
)
echo ✅ 依赖安装完成

:: 创建目录
echo [3/4] 📁 创建必要目录...
if not exist "backend\logs" mkdir backend\logs
if not exist "backend\config" mkdir backend\config
if not exist "data" mkdir data
echo ✅ 目录创建完成

:: 检查配置
echo [4/4] ⚙️ 检查系统配置...
if not exist "backend\config\user_config.json" (
    echo ⚠️  未找到Token配置文件
    echo 💡 请先配置API Token才能正常使用系统
    echo.
    echo 🔧 配置方式：
    echo    1. 运行 update_tokens.bat 配置工具
    echo    2. 或访问 http://localhost:3001/settings 网页配置
    echo.
    set /p choice="是否现在配置Token？(Y/N): "
    if /i "%choice%"=="Y" (
        echo 🚀 启动Token配置工具...
        call update_tokens.bat
    )
)
echo ✅ 配置检查完成

:: 启动系统
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      🚀 启动系统服务                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔥 正在启动量化交易系统...
echo.
echo 📊 后端API服务: http://localhost:8000
echo 🌐 前端Web界面: http://localhost:3001
echo 📚 API文档: http://localhost:8000/docs
echo 🏥 健康检查: http://localhost:8000/health
echo.

:: 启动后端
echo 🚀 启动后端服务...
start "量化交易系统-后端API" cmd /k "title 后端API服务 && cd backend && venv\Scripts\activate.bat && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

:: 等待后端启动
echo ⏳ 等待后端服务初始化...
timeout /t 5 /nobreak >nul

:: 启动前端
echo 🚀 启动前端服务...
start "量化交易系统-前端界面" cmd /k "title 前端Web界面 && cd frontend && set PORT=3001 && npm start"

:: 等待前端启动
echo ⏳ 等待前端服务初始化...
timeout /t 3 /nobreak >nul

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                      ✅ 启动完成！                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎉 系统启动成功！
echo.
echo 📝 使用说明：
echo    • 主界面：http://localhost:3001
echo    • 系统设置：http://localhost:3001/settings
echo    • API文档：http://localhost:8000/docs
echo.
echo 🛑 停止系统：关闭两个命令行窗口即可
echo.
echo 📚 更多帮助：查看 客户使用指南.md 文件
echo.

:: 自动打开浏览器
echo 🌐 正在打开系统界面...
timeout /t 2 /nobreak >nul
start http://localhost:3001

echo.
echo 按任意键退出启动器...
pause >nul
