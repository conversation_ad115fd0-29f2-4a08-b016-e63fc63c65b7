# 📋 量化交易系统 - 客户使用指南

## 🎯 快速开始（推荐方式）

### 方式一：一键启动（最简单）
```
双击运行：start_system.bat
```
这个脚本会自动：
- ✅ 检查环境（Python、Node.js）
- ✅ 安装依赖包
- ✅ 创建必要目录
- ✅ 启动前后端服务

### 方式二：分步安装（稳定可靠）
```
1. 双击运行：step1_install.bat  （安装依赖）
2. 双击运行：step2_start.bat    （启动系统）
```

## 🔧 Token配置（必需步骤）

### 配置方式一：命令行工具（推荐）
```
双击运行：update_tokens.bat
```
按提示输入：
- Tushare Token（必需）
- DeepSeek API Key（可选）

### 配置方式二：Web界面
1. 启动系统后访问：http://localhost:3001/settings
2. 输入Token并保存

### 配置方式三：手动编辑
编辑文件：`backend/config/user_config.json`
```json
{
  "tushare_token": "您的Tushare_Token",
  "deepseek_api_key": "您的DeepSeek_API_Key"
}
```

## 🌐 系统访问地址

启动成功后访问：
- **主界面**：http://localhost:3001
- **API文档**：http://localhost:8000/docs
- **健康检查**：http://localhost:8000/health

## 📁 文件说明

### 🚀 启动文件
| 文件名 | 用途 | 推荐度 |
|--------|------|--------|
| `start_system.bat` | 一键启动（中文界面） | ⭐⭐⭐⭐⭐ |
| `step1_install.bat` | 分步安装依赖（英文） | ⭐⭐⭐⭐ |
| `step2_start.bat` | 分步启动系统（英文） | ⭐⭐⭐⭐ |

### 🔧 配置文件
| 文件名 | 用途 | 推荐度 |
|--------|------|--------|
| `update_tokens.bat` | Token配置工具 | ⭐⭐⭐⭐⭐ |

### 🧪 测试文件
| 文件名 | 用途 |
|--------|------|
| `quick_test.bat` | 快速功能测试 |
| `check_env.bat` | 环境检查 |

### 📚 文档文件
| 文件名 | 内容 |
|--------|------|
| `README.md` | 系统总体说明 |
| `TOKEN_CONFIGURATION_GUIDE.md` | Token配置详细指南 |
| `QUICK_TOKEN_SETUP.md` | 快速Token配置 |

## 🎯 客户使用流程

### 首次使用
1. **环境准备**：确保安装了Python 3.8+和Node.js 16+
2. **启动系统**：双击 `start_system.bat`
3. **配置Token**：双击 `update_tokens.bat` 配置API Token
4. **访问系统**：打开浏览器访问 http://localhost:3001

### 日常使用
1. **启动**：双击 `start_system.bat` 或 `step2_start.bat`
2. **访问**：浏览器打开 http://localhost:3001
3. **停止**：关闭命令行窗口

## 🔐 Token获取指南

### Tushare Token（必需）
1. 访问：https://tushare.pro/
2. 注册账号并实名认证
3. 在个人中心获取Token

### DeepSeek API Key（可选，用于AI分析）
1. 访问：https://platform.deepseek.com/
2. 注册账号
3. 创建API Key

## ❗ 常见问题

### 1. 启动失败
**问题**：双击bat文件后出现错误
**解决**：
- 检查是否安装Python 3.8+
- 检查是否安装Node.js 16+
- 以管理员身份运行bat文件

### 2. 端口占用
**问题**：提示端口8000或3001被占用
**解决**：
- 关闭其他占用端口的程序
- 或修改配置文件中的端口号

### 3. Token配置无效
**问题**：Token配置后仍无法获取数据
**解决**：
- 检查Token格式是否正确
- 确认Tushare账号有足够积分
- 重启系统使配置生效

### 4. 网络连接问题
**问题**：无法获取实时数据
**解决**：
- 检查网络连接
- 确认防火墙设置
- 检查API服务状态

## 📞 技术支持

### 日志查看
- 后端日志：`backend/logs/` 目录
- 前端日志：浏览器开发者工具控制台

### 系统检查
运行 `check_env.bat` 检查环境配置

### 功能测试
运行 `quick_test.bat` 进行快速功能测试

## 🎉 使用建议

1. **首次使用**：建议使用 `start_system.bat` 一键启动
2. **生产环境**：建议使用分步启动方式（step1 + step2）
3. **Token配置**：优先使用 `update_tokens.bat` 命令行工具
4. **定期备份**：备份 `backend/config/user_config.json` 配置文件
5. **系统更新**：重新运行 `step1_install.bat` 更新依赖

---

**注意**：所有bat文件都支持中英文Windows系统，建议客户优先使用 `start_system.bat` 进行一键启动。
