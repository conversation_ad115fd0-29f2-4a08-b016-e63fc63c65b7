@echo off
chcp 65001 >nul
title Quantitative Trading System Launcher
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              🚀 Quantitative Trading System Launcher          ║
echo ║                     量化交易系统启动器                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Check environment
echo [1/4] 🔍 Checking runtime environment...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python not found
    echo 💡 Please install Python 3.8 or higher
    echo 📥 Download: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Node.js not found
    echo 💡 Please install Node.js 16 or higher
    echo 📥 Download: https://nodejs.org/
    echo.
    pause
    exit /b 1
)
echo ✅ Environment check passed

:: Install dependencies
echo [2/4] 📦 Installing system dependencies...
if not exist "backend\venv" (
    echo 🔧 Creating Python virtual environment...
    cd backend
    python -m venv venv
    cd ..
)

echo 🔧 Installing backend dependencies...
cd backend
call venv\Scripts\activate.bat
pip install -q -r requirements.txt >nul 2>&1
cd ..

if not exist "frontend\node_modules" (
    echo 🔧 Installing frontend dependencies...
    cd frontend
    npm install --silent >nul 2>&1
    cd ..
)
echo ✅ Dependencies installation completed

:: Create directories
echo [3/4] 📁 Creating necessary directories...
if not exist "backend\logs" mkdir backend\logs
if not exist "backend\config" mkdir backend\config
if not exist "data" mkdir data
echo ✅ Directories created

:: Check configuration
echo [4/4] ⚙️ Checking system configuration...
if not exist "backend\config\user_config.json" (
    echo ⚠️  Token configuration file not found
    echo 💡 Please configure API tokens before using the system
    echo.
    echo 🔧 Configuration methods:
    echo    1. Run update_tokens.bat configuration tool
    echo    2. Or visit http://localhost:3001/settings for web configuration
    echo.
    set /p choice="Configure tokens now? (Y/N): "
    if /i "%choice%"=="Y" (
        echo 🚀 Starting token configuration tool...
        call update_tokens.bat
    )
)
echo ✅ Configuration check completed

:: Start system
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 Starting System Services                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔥 Starting Quantitative Trading System...
echo.
echo 📊 Backend API Service: http://localhost:8000
echo 🌐 Frontend Web Interface: http://localhost:3001
echo 📚 API Documentation: http://localhost:8000/docs
echo 🏥 Health Check: http://localhost:8000/health
echo.

:: Start backend
echo 🚀 Starting backend service...
start "Quant-Trading-Backend" cmd /k "title Backend API Service && cd backend && venv\Scripts\activate.bat && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

:: Wait for backend
echo ⏳ Waiting for backend service initialization...
timeout /t 5 /nobreak >nul

:: Start frontend
echo 🚀 Starting frontend service...
start "Quant-Trading-Frontend" cmd /k "title Frontend Web Interface && cd frontend && set PORT=3001 && npm start"

:: Wait for frontend
echo ⏳ Waiting for frontend service initialization...
timeout /t 3 /nobreak >nul

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        ✅ Startup Complete!                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🎉 System started successfully!
echo.
echo 📝 Usage Instructions:
echo    • Main Interface: http://localhost:3001
echo    • System Settings: http://localhost:3001/settings
echo    • API Documentation: http://localhost:8000/docs
echo.
echo 🛑 To stop system: Close both command line windows
echo.
echo 📚 For more help: Check CLIENT_USER_GUIDE.md file
echo.

:: Auto open browser
echo 🌐 Opening system interface...
timeout /t 2 /nobreak >nul
start http://localhost:3001

echo.
echo Press any key to exit launcher...
pause >nul
