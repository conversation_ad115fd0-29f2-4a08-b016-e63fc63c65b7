const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

async function buildFrontend() {
  console.log('🎨 开始构建前端...');
  
  const frontendPath = path.join(__dirname, '../../frontend');
  const outputPath = path.join(__dirname, '../resources/frontend');
  
  // 检查前端目录是否存在
  if (!fs.existsSync(frontendPath)) {
    console.error('❌ 前端目录不存在:', frontendPath);
    process.exit(1);
  }
  
  // 创建输出目录
  if (!fs.existsSync(path.dirname(outputPath))) {
    fs.mkdirSync(path.dirname(outputPath), { recursive: true });
  }
  
  try {
    // 检查是否已安装依赖
    const nodeModulesPath = path.join(frontendPath, 'node_modules');
    if (!fs.existsSync(nodeModulesPath)) {
      console.log('📦 安装前端依赖...');
      await runCommand('npm', ['install'], frontendPath);
    }
    
    // 构建前端
    console.log('🔨 构建前端应用...');
    await runCommand('npm', ['run', 'build'], frontendPath);
    
    // 复制构建结果
    const buildPath = path.join(frontendPath, 'build');
    if (fs.existsSync(buildPath)) {
      console.log('📋 复制构建文件...');
      copyDirectory(buildPath, outputPath);
      console.log('✅ 前端构建成功');
    } else {
      throw new Error('构建目录不存在: ' + buildPath);
    }
    
  } catch (error) {
    console.error('❌ 前端构建失败:', error);
    process.exit(1);
  }
}

function runCommand(command, args, cwd) {
  return new Promise((resolve, reject) => {
    console.log(`🔄 执行命令: ${command} ${args.join(' ')}`);
    
    const process = spawn(command, args, {
      cwd: cwd,
      stdio: 'inherit',
      shell: true
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`命令执行失败，退出代码: ${code}`));
      }
    });
    
    process.on('error', (error) => {
      reject(error);
    });
  });
}

function copyDirectory(source, target) {
  // 如果目标目录存在，先删除
  if (fs.existsSync(target)) {
    fs.rmSync(target, { recursive: true, force: true });
  }
  
  // 创建目标目录
  fs.mkdirSync(target, { recursive: true });
  
  const files = fs.readdirSync(source);
  
  files.forEach(file => {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);
    
    if (fs.statSync(sourcePath).isDirectory()) {
      copyDirectory(sourcePath, targetPath);
    } else {
      fs.copyFileSync(sourcePath, targetPath);
    }
  });
}

// 如果直接运行此脚本
if (require.main === module) {
  buildFrontend();
}

module.exports = buildFrontend;
