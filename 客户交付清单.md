# 📦 量化交易系统 - 客户交付清单

## 🎯 推荐使用方式（按优先级排序）

### 🥇 第一选择：中文界面一键启动
```
双击运行：启动系统.bat
```
- ✅ 完全中文界面
- ✅ 自动检查环境
- ✅ 自动安装依赖
- ✅ 自动配置提醒
- ✅ 自动打开浏览器

### 🥈 第二选择：原版一键启动
```
双击运行：start_system.bat
```
- ✅ 中文界面
- ✅ 功能完整
- ✅ 稳定可靠

### 🥉 第三选择：英文界面启动
```
双击运行：START_SYSTEM_EN.bat
```
- ✅ 英文界面
- ✅ 适合英文系统

### 🔧 第四选择：分步启动（最稳定）
```
1. 双击：step1_install.bat  （安装依赖）
2. 双击：step2_start.bat    （启动系统）
```
- ✅ 分步执行
- ✅ 错误定位容易
- ✅ 适合生产环境

## 🔑 Token配置方式

### 推荐方式：命令行工具
```
双击运行：update_tokens.bat
```

### 备选方式：Web界面
```
访问：http://localhost:3001/settings
```

## 📁 文件用途说明

### 🚀 启动文件（客户主要使用）
| 文件名 | 界面语言 | 推荐度 | 说明 |
|--------|----------|--------|------|
| `启动系统.bat` | 中文 | ⭐⭐⭐⭐⭐ | **最推荐**，完整中文界面 |
| `start_system.bat` | 中文 | ⭐⭐⭐⭐ | 原版启动脚本 |
| `START_SYSTEM_EN.bat` | 英文 | ⭐⭐⭐ | 英文系统用户使用 |
| `step1_install.bat` | 英文 | ⭐⭐⭐ | 分步安装（稳定） |
| `step2_start.bat` | 英文 | ⭐⭐⭐ | 分步启动（稳定） |

### 🔧 配置文件
| 文件名 | 用途 | 推荐度 |
|--------|------|--------|
| `update_tokens.bat` | Token配置工具 | ⭐⭐⭐⭐⭐ |

### 🧪 测试文件（可选使用）
| 文件名 | 用途 |
|--------|------|
| `quick_test.bat` | 快速功能测试 |
| `check_env.bat` | 环境检查 |

### 📚 文档文件
| 文件名 | 内容 | 目标用户 |
|--------|------|----------|
| `客户使用指南.md` | 中文使用说明 | 中文用户 |
| `CLIENT_USER_GUIDE.md` | 英文使用说明 | 英文用户 |
| `TOKEN_CONFIGURATION_GUIDE.md` | Token配置详细指南 | 所有用户 |
| `QUICK_TOKEN_SETUP.md` | 快速Token配置 | 所有用户 |
| `README.md` | 系统总体说明 | 开发者/管理员 |

### 🗑️ 可以删除的文件（开发用）
以下文件是开发过程中的测试文件，客户可以删除：
- `start_backend.bat`
- `start_backend_only.bat`
- `start_frontend.bat`
- `start_frontend.ps1`
- `start_manual.bat`
- `start_simple.bat`
- `test_backend.py`
- 所有 `backend/test_*.py` 文件

## 🎯 客户使用流程

### 首次使用（推荐流程）
1. **双击运行**：`启动系统.bat`
2. **按提示配置Token**：选择Y配置Token
3. **等待启动完成**：系统会自动打开浏览器
4. **开始使用**：访问 http://localhost:3001

### 日常使用
1. **启动**：双击 `启动系统.bat`
2. **使用**：浏览器自动打开系统界面
3. **停止**：关闭命令行窗口

## 🔐 Token获取指南

### Tushare Token（必需）
1. 访问：https://tushare.pro/
2. 注册并实名认证
3. 获取Token

### DeepSeek API Key（可选）
1. 访问：https://platform.deepseek.com/
2. 注册账号
3. 创建API Key

## 📞 技术支持

### 问题排查顺序
1. 检查Python和Node.js是否安装
2. 以管理员身份运行bat文件
3. 查看命令行窗口的错误信息
4. 检查Token配置是否正确

### 日志位置
- 后端日志：`backend/logs/`
- 前端日志：浏览器开发者工具

## 🎉 交付建议

### 给客户的建议
1. **首选方式**：使用 `启动系统.bat` 一键启动
2. **配置Token**：使用 `update_tokens.bat` 配置工具
3. **查看文档**：遇到问题先查看 `客户使用指南.md`
4. **备份配置**：定期备份 `backend/config/user_config.json`

### 系统要求
- Windows 10/11
- Python 3.8+
- Node.js 16+
- 4GB+ RAM
- 2GB+ 磁盘空间

---

**总结**：客户主要使用 `启动系统.bat` 和 `update_tokens.bat` 两个文件即可完成系统的启动和配置。其他文件作为备选方案或特殊情况使用。
