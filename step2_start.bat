@echo off
echo ========================================
echo Step 2: Start System
echo ========================================
echo.

:: Get script directory
set "SCRIPT_DIR=%~dp0"
echo Script directory: %SCRIPT_DIR%

:: Check if backend dependencies are installed
if not exist "%SCRIPT_DIR%backend\venv" (
    echo ERROR: Backend virtual environment not found
    echo Please run step1_install.bat first
    pause
    exit /b 1
)

:: Check if frontend dependencies are installed
if not exist "%SCRIPT_DIR%frontend\node_modules" (
    echo ERROR: Frontend node_modules not found
    echo Please run step1_install.bat first
    pause
    exit /b 1
)

echo.
echo Starting Quantitative Trading System...
echo.
echo Backend API: http://localhost:8000
echo Frontend UI: http://localhost:3000
echo API Documentation: http://localhost:8000/docs
echo Health Check: http://localhost:8000/health
echo.

:: Start backend service
echo Starting backend service...
start "Quant-Backend" cmd /k "cd /d \"%SCRIPT_DIR%backend\" && venv\Scripts\activate.bat && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

:: Wait a moment
echo Waiting for backend to initialize...
timeout /t 3 /nobreak >nul

:: Start frontend service
echo Starting frontend service...
start "Quant-Frontend" cmd /k "cd /d \"%SCRIPT_DIR%frontend\" && npm start"

echo.
echo ========================================
echo System startup initiated!
echo ========================================
echo.
echo Two command windows should have opened:
echo 1. Backend service (Python/FastAPI)
echo 2. Frontend service (React/Node.js)
echo.
echo Wait for both services to start, then open your browser to:
echo http://localhost:3000
echo.
echo To stop the system, close both command windows
echo.
pause
