# 📋 Quantitative Trading System - Client User Guide

## 🎯 Quick Start (Recommended)

### Method 1: One-Click Launch (Easiest)
```
Double-click: start_system.bat
```
This script will automatically:
- ✅ Check environment (Python, Node.js)
- ✅ Install dependencies
- ✅ Create necessary directories
- ✅ Start frontend and backend services

### Method 2: Step-by-Step Installation (Stable & Reliable)
```
1. Double-click: step1_install.bat  (Install dependencies)
2. Double-click: step2_start.bat    (Start system)
```

## 🔧 Token Configuration (Required)

### Method 1: Command Line Tool (Recommended)
```
Double-click: update_tokens.bat
```
Follow prompts to enter:
- Tushare Token (Required)
- DeepSeek API Key (Optional)

### Method 2: Web Interface
1. After starting system, visit: http://localhost:3001/settings
2. Enter tokens and save

### Method 3: Manual Edit
Edit file: `backend/config/user_config.json`
```json
{
  "tushare_token": "Your_Tushare_Token",
  "deepseek_api_key": "Your_DeepSeek_API_Key"
}
```

## 🌐 System Access URLs

After successful startup:
- **Main Interface**: http://localhost:3001
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

## 📁 File Descriptions

### 🚀 Startup Files
| File | Purpose | Recommendation |
|------|---------|----------------|
| `start_system.bat` | One-click startup (Chinese UI) | ⭐⭐⭐⭐⭐ |
| `step1_install.bat` | Step-by-step install (English) | ⭐⭐⭐⭐ |
| `step2_start.bat` | Step-by-step start (English) | ⭐⭐⭐⭐ |

### 🔧 Configuration Files
| File | Purpose | Recommendation |
|------|---------|----------------|
| `update_tokens.bat` | Token configuration tool | ⭐⭐⭐⭐⭐ |

### 🧪 Testing Files
| File | Purpose |
|------|---------|
| `quick_test.bat` | Quick functionality test |
| `check_env.bat` | Environment check |

### 📚 Documentation Files
| File | Content |
|------|---------|
| `README.md` | System overview |
| `TOKEN_CONFIGURATION_GUIDE.md` | Detailed token configuration |
| `QUICK_TOKEN_SETUP.md` | Quick token setup |

## 🎯 Client Usage Workflow

### First Time Setup
1. **Environment**: Ensure Python 3.8+ and Node.js 16+ are installed
2. **Start System**: Double-click `start_system.bat`
3. **Configure Tokens**: Double-click `update_tokens.bat` to configure API tokens
4. **Access System**: Open browser and visit http://localhost:3001

### Daily Usage
1. **Start**: Double-click `start_system.bat` or `step2_start.bat`
2. **Access**: Open browser to http://localhost:3001
3. **Stop**: Close command line windows

## 🔐 Token Acquisition Guide

### Tushare Token (Required)
1. Visit: https://tushare.pro/
2. Register account and complete identity verification
3. Get token from personal center

### DeepSeek API Key (Optional, for AI analysis)
1. Visit: https://platform.deepseek.com/
2. Register account
3. Create API Key

## ❗ Common Issues

### 1. Startup Failure
**Problem**: Error when double-clicking bat files
**Solution**:
- Check if Python 3.8+ is installed
- Check if Node.js 16+ is installed
- Run bat file as administrator

### 2. Port Occupied
**Problem**: Port 8000 or 3001 is already in use
**Solution**:
- Close other programs using these ports
- Or modify port numbers in configuration files

### 3. Invalid Token Configuration
**Problem**: Data cannot be retrieved after token configuration
**Solution**:
- Check if token format is correct
- Ensure Tushare account has sufficient credits
- Restart system to apply configuration

### 4. Network Connection Issues
**Problem**: Cannot retrieve real-time data
**Solution**:
- Check network connection
- Verify firewall settings
- Check API service status

## 📞 Technical Support

### Log Viewing
- Backend logs: `backend/logs/` directory
- Frontend logs: Browser developer tools console

### System Check
Run `check_env.bat` to check environment configuration

### Functionality Test
Run `quick_test.bat` for quick functionality testing

## 🎉 Usage Recommendations

1. **First Use**: Recommend using `start_system.bat` for one-click startup
2. **Production Environment**: Recommend step-by-step startup (step1 + step2)
3. **Token Configuration**: Prioritize using `update_tokens.bat` command line tool
4. **Regular Backup**: Backup `backend/config/user_config.json` configuration file
5. **System Updates**: Re-run `step1_install.bat` to update dependencies

---

**Note**: All bat files support both Chinese and English Windows systems. Clients are recommended to use `start_system.bat` for one-click startup first.
