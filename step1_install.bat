@echo off
echo ========================================
echo Step 1: Install Dependencies
echo ========================================
echo.

:: Get script directory
set "SCRIPT_DIR=%~dp0"
echo Script directory: %SCRIPT_DIR%

:: Check Python
echo Checking Python...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

:: Check Node.js
echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)

:: Install backend dependencies
echo.
echo Installing backend dependencies...
cd /d "%SCRIPT_DIR%backend"
echo Current directory: %CD%

if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Installing Python packages...
pip install fastapi uvicorn pandas numpy tushare requests

if %errorlevel% neq 0 (
    echo ERROR: Backend installation failed
    pause
    exit /b 1
)

echo Backend dependencies installed successfully!

:: Install frontend dependencies
echo.
echo Installing frontend dependencies...
cd /d "%SCRIPT_DIR%frontend"
echo Current directory: %CD%

if not exist "node_modules" (
    echo Installing npm packages...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Frontend installation failed
        pause
        exit /b 1
    )
)

echo Frontend dependencies installed successfully!

:: Create directories
echo.
echo Creating necessary directories...
cd /d "%SCRIPT_DIR%"
if not exist "backend\logs" mkdir "backend\logs"
if not exist "data" mkdir "data"

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo Next step: Run step2_start.bat to start the system
echo.
pause
