@echo off
echo ========================================
echo Quick Test for Quantitative Trading System
echo ========================================
echo.

echo [1/4] Testing Python environment and dependencies...
cd backend
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    python -c "import fastapi, pandas, numpy, tushare; print('Core dependencies check passed')"
    if %errorlevel% neq 0 (
        echo Python dependencies check failed
        pause
        exit /b 1
    )
) else (
    echo Virtual environment does not exist, please run start_system.bat first
    pause
    exit /b 1
)
cd ..

echo [2/4] Testing configuration files...
if exist "backend\.env" (
    echo Backend environment config file exists
) else (
    echo Backend environment config file missing
)

if exist "frontend\.env" (
    echo Frontend environment config file exists
) else (
    echo Frontend environment config file missing
)

echo [3/4] Testing API connections...
cd backend
call venv\Scripts\activate.bat
python -c "import sys; sys.path.append('.'); from data.tushare_client import get_tushare_client; print('Tushare client test passed')"
cd ..

echo [4/4] Testing frontend dependencies...
cd frontend
if exist "node_modules" (
    echo Frontend dependencies installed
) else (
    echo Frontend dependencies not installed, please run start_system.bat first
)
cd ..

echo.
echo ========================================
echo Quick test completed!
echo ========================================
echo.
echo If all tests pass, you can run start_system.bat to start the system
echo.
pause
